/**
 * @file llm-service.js - LLM服务管理类
 * @description 管理Gemini AI服务，提供统一的LLM处理接口
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class LLMService - LLM服务管理类
 * @description 管理Gemini AI服务
 */
class LLMService {
    /**
     * @function constructor - 构造函数
     */
    constructor() {
        // 初始化提示词管理器
        this.promptManager = new PromptManager();

        // Gemini 状态
        this.geminiStatus = {
            connectionStatus: 'checking',
            lastCheckTime: null,
            consecutiveFailures: 0,
            isChecking: false,
            lastSuccessTime: null
        };

        // 当前使用的 LLM
        this.currentLLM = 'gemini';
        
        // LLM优化：响应缓存机制
        this.responseCache = new Map();
        this.cacheConfig = {
            maxSize: 100,
            ttl: 5 * 60 * 1000 // 5分钟TTL
        };
    }



    /**
     * @function checkGeminiConnection - 检测Gemini API连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkGeminiConnection() {
        if (this.geminiStatus.isChecking) {
            logger.debug('Gemini', '连接检测正在进行中，跳过重复检测');
            return this.geminiStatus.connectionStatus === 'connected';
        }

        this.geminiStatus.isChecking = true;
        const previousStatus = this.geminiStatus.connectionStatus;
        this.geminiStatus.connectionStatus = 'checking';

        logger.info('Gemini', '开始检测Gemini API连接状态', {
            previousStatus: previousStatus,
            consecutiveFailures: this.geminiStatus.consecutiveFailures
        });

        try {
            // 检查API Key是否存在
            if (!SYSTEM_CONFIG.API.GEMINI.API_KEY) {
                logger.warn('Gemini', 'Gemini API Key未配置');
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;
                return false;
            }

            // 发送测试请求
            const startTime = Date.now();
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: 'ping' }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 5
                        }
                    }),
                    signal: AbortSignal.timeout(30000) // 30秒超时
                }
            );

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const wasDisconnected = previousStatus === 'disconnected';
                this.geminiStatus.connectionStatus = 'connected';
                this.geminiStatus.lastCheckTime = new Date();
                this.geminiStatus.lastSuccessTime = new Date();
                this.geminiStatus.consecutiveFailures = 0;

                logger.success('Gemini', 'Gemini API连接成功', {
                    responseTime: `${responseTime}ms`,
                    statusChanged: wasDisconnected
                });

                return true;
            } else {
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;

                logger.error('Gemini', `Gemini API连接失败: ${response.status}`, {
                    responseTime: `${responseTime}ms`,
                    consecutiveFailures: this.geminiStatus.consecutiveFailures
                });

                return false;
            }
        } catch (error) {
            this.geminiStatus.connectionStatus = 'disconnected';
            this.geminiStatus.consecutiveFailures++;

            logger.error('Gemini', 'Gemini API连接检测失败', {
                error: error.message,
                consecutiveFailures: this.geminiStatus.consecutiveFailures
            });

            return false;
        } finally {
            this.geminiStatus.isChecking = false;
            this.geminiStatus.lastCheckTime = new Date();
        }
    }

    /**
     * @function processOrderText - 处理订单文本
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async processOrderText(text, otaType = 'auto') {
        const startTime = Date.now();
        
        // LLM优化：检查缓存
        const cacheKey = this.generateCacheKey(text, otaType);
        const cachedResult = this.getCachedResponse(cacheKey);
        
        if (cachedResult) {
            logger.info('LLM', '使用缓存结果', {
                textLength: text.length,
                otaType: otaType,
                cacheHit: true
            });
            
            return {
                ...cachedResult,
                processingTime: Date.now() - startTime,
                fromCache: true
            };
        }
        
        logger.info('LLM', '开始处理订单文本', {
            textLength: text.length,
            otaType: otaType
        });

        try {
            // 使用Gemini处理
            logger.info('LLM', '开始调用Gemini API');
            const geminiStartTime = Date.now();
            const geminiResult = await this.callGemini(text, otaType);
            const geminiProcessingTime = Date.now() - geminiStartTime;
            
            logger.info('LLM', 'Gemini API调用完成', {
                processingTime: geminiProcessingTime,
                success: geminiResult.success,
                error: geminiResult.error || null
            });
            
            if (geminiResult.success) {
                const result = {
                    success: true,
                    data: geminiResult.data,
                    provider: 'gemini',
                    processingTime: Date.now() - startTime,
                    apiCallTime: geminiProcessingTime
                };
                
                // LLM优化：缓存成功结果
                this.setCachedResponse(cacheKey, result);
                
                logger.success('LLM', 'Gemini处理成功', {
                    totalProcessingTime: result.processingTime,
                    apiCallTime: geminiProcessingTime,
                    provider: 'gemini'
                });
                
                return result;
            }

            // Gemini失败
            const totalFailTime = Date.now() - startTime;
            logger.error('LLM', 'Gemini处理失败', {
                geminiError: geminiResult.error,
                geminiProcessingTime: geminiProcessingTime,
                totalProcessingTime: totalFailTime
            });

            return {
                success: false,
                error: 'Gemini处理失败',
                processingTime: totalFailTime,
                details: {
                    geminiError: geminiResult.error,
                    geminiTime: geminiProcessingTime
                }
            };

        } catch (error) {
            logger.error('LLM', 'LLM处理异常', { error: error.message });
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }



    /**
     * @function callGemini - 调用Gemini API
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async callGemini(text, otaType) {
        const requestStartTime = Date.now();
        
        try {
            const prompt = this.promptManager.getOTAPrompt(otaType, text, new Date().toISOString().split('T')[0]);
            
            // 调试日志：显示发送给Gemini的内容
            console.log('=== Gemini 调试信息 ===');
            console.log('订单文本:', text);
            console.log('OTA类型:', otaType);
            console.log('完整Prompt:', prompt);
            
            const requestBody = {
                contents: [{ parts: [{ text: prompt }] }],
                generationConfig: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG
            };
            
            // 调试日志：显示请求体
            console.log('请求体:', JSON.stringify(requestBody, null, 2));
            console.log('=======================');
            
            logger.debug('Gemini', '发送API请求', {
                textLength: text.length,
                otaType: otaType,
                timeout: SYSTEM_CONFIG.API.GEMINI.TIMEOUT
            });
            
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody),
                    signal: AbortSignal.timeout(SYSTEM_CONFIG.API.GEMINI.TIMEOUT)
                }
            );

            const responseTime = Date.now() - requestStartTime;
            
            if (!response.ok) {
                const errorMsg = `Gemini API请求失败: ${response.status} ${response.statusText}`;
                logger.error('Gemini', errorMsg, {
                    responseTime: responseTime,
                    status: response.status,
                    statusText: response.statusText
                });
                throw new Error(errorMsg);
            }

            const data = await response.json();
            
            // 调试日志：显示Gemini返回的原始响应
            console.log('=== Gemini 响应信息 ===');
            console.log('原始JSON响应:', JSON.stringify(data, null, 2));
            
            const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
            
            // 调试日志：显示提取的内容
            console.log('提取的内容:', content);
            
            const parseResult = this.parseResponse(content);
            
            // 调试日志：显示解析结果
            console.log('解析结果:', JSON.stringify(parseResult, null, 2));
            console.log('=======================');
            
            const totalTime = Date.now() - requestStartTime;
            logger.debug('Gemini', 'API请求成功', {
                responseTime: totalTime,
                contentLength: content.length
            });

            return {
                success: true,
                data: parseResult,
                requestTime: totalTime
            };

        } catch (error) {
            const errorTime = Date.now() - requestStartTime;
            const errorMsg = `Gemini调用失败: ${error.message}`;
            
            // 调试日志：显示错误信息
            console.log('=== Gemini 错误信息 ===');
            console.log('错误消息:', error.message);
            console.log('错误类型:', error.name);
            console.log('错误堆栈:', error.stack);
            console.log('=======================');
            
            logger.error('Gemini', errorMsg, {
                requestTime: errorTime,
                errorType: error.name,
                isTimeout: error.message.includes('timeout') || error.message.includes('aborted')
            });
            
            return {
                success: false,
                error: errorMsg,
                requestTime: errorTime
            };
        }
    }

    /**
     * @function generateCacheKey - 生成缓存键
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {string} 缓存键
     */
    generateCacheKey(text, otaType) {
        // 使用文本内容和OTA类型生成简单哈希
        const content = `${text.trim()}_${otaType}`;
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return `llm_${Math.abs(hash).toString(36)}`;
    }

    /**
     * @function getCachedResponse - 获取缓存响应
     * @param {string} cacheKey - 缓存键
     * @returns {object|null} 缓存的响应或null
     */
    getCachedResponse(cacheKey) {
        const cached = this.responseCache.get(cacheKey);
        if (!cached) {
            return null;
        }

        // 检查TTL
        if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
            this.responseCache.delete(cacheKey);
            return null;
        }

        return cached.data;
    }

    /**
     * @function setCachedResponse - 设置缓存响应
     * @param {string} cacheKey - 缓存键
     * @param {object} data - 要缓存的数据
     */
    setCachedResponse(cacheKey, data) {
        // 检查缓存大小限制
        if (this.responseCache.size >= this.cacheConfig.maxSize) {
            // 删除最旧的缓存项
            const firstKey = this.responseCache.keys().next().value;
            this.responseCache.delete(firstKey);
        }

        this.responseCache.set(cacheKey, {
            data: data,
            timestamp: Date.now()
        });
    }

    /**
     * @function clearCache - 清空缓存
     */
    clearCache() {
        this.responseCache.clear();
        logger.info('LLM', '缓存已清空');
    }

    /**
     * @function parseResponse - 解析LLM响应
     * @param {string} content - LLM响应内容
     * @returns {object} 解析后的数据
     */
    parseResponse(content) {
        console.log('=== 开始解析LLM响应 ===');
        console.log('原始内容长度:', content ? content.length : 0);
        console.log('原始内容:', content);
        console.log('内容类型:', typeof content);
        
        try {
            const orders = [];
            
            // 检查内容是否为空
            if (!content || content.trim() === '') {
                console.log('内容为空，返回空订单列表');
                return {
                    rawContent: content,
                    orders: [],
                    metadata: { 
                        error: '内容为空',
                        parseSuccess: false,
                        totalOrders: 0
                    }
                };
            }
            
            // 预处理内容：移除可能的隐藏字符
            const cleanContent = content
                .replace(/\r\n/g, '\n')  // 统一换行符
                .replace(/\r/g, '\n')    // 统一换行符
                .replace(/[\u200B-\u200D\uFEFF]/g, '')  // 移除零宽字符
                .trim();
            
            console.log('清理后的内容:', cleanContent);
            console.log('清理后的内容长度:', cleanContent.length);
            
            // 解析结构化文本格式的订单数据
            const orderData = this.parseStructuredText(cleanContent);
            
            console.log('parseStructuredText返回的数据:', orderData);
            console.log('orderData是否为对象:', typeof orderData === 'object');
            console.log('orderData键数量:', orderData ? Object.keys(orderData).length : 0);
            
            if (orderData && typeof orderData === 'object' && Object.keys(orderData).length > 0) {
                console.log('开始转换为标准订单格式...');
                
                // 转换为标准订单格式
                const standardOrder = this.convertToStandardOrder(orderData);
                
                console.log('convertToStandardOrder返回的数据:', standardOrder);
                
                if (standardOrder && typeof standardOrder === 'object') {
                    orders.push(standardOrder);
                    console.log('成功解析订单:', standardOrder);
                } else {
                    console.log('convertToStandardOrder返回了无效数据');
                }
            } else {
                console.log('parseStructuredText返回了空数据或无效数据');
                console.log('可能的原因:');
                console.log('1. 文本格式不符合预期');
                console.log('2. 字段映射失败');
                console.log('3. 冒号分隔符识别失败');
            }
            
            console.log('=== 解析完成 ===');
            console.log('解析出的订单数量:', orders.length);
            
            const result = {
                rawContent: content,
                orders: orders,
                metadata: {
                    totalOrders: orders.length,
                    parseSuccess: orders.length > 0,
                    cleanedContent: cleanContent,
                    orderDataKeys: orderData ? Object.keys(orderData) : []
                }
            };
            
            console.log('最终返回结果:', result);
            return result;
            
        } catch (error) {
            console.error('解析LLM响应时出错:', error);
            console.error('错误堆栈:', error.stack);
            return {
                rawContent: content,
                orders: [],
                metadata: {
                    error: error.message,
                    parseSuccess: false,
                    totalOrders: 0,
                    errorStack: error.stack
                }
            };
        }
    }
    
    /**
     * @function parseStructuredText - 解析结构化文本
     * @param {string} text - 结构化文本
     * @returns {object} 解析后的订单数据
     */
    parseStructuredText(text) {
        console.log('=== 开始解析结构化文本 ===');
        console.log('原始文本长度:', text.length);
        console.log('原始文本内容:', JSON.stringify(text));
        
        const orderData = {};
        const lines = text.split('\n');
        
        console.log('分割后的行数:', lines.length);
        
        for (let i = 0; i < lines.length; i++) {
            const originalLine = lines[i];
            const trimmedLine = originalLine.trim();
            
            console.log(`处理第${i+1}行:`, {
                original: JSON.stringify(originalLine),
                trimmed: JSON.stringify(trimmedLine),
                length: trimmedLine.length
            });
            
            // 跳过空行和分隔符行
            if (trimmedLine === '' || trimmedLine.startsWith('=')) {
                console.log(`跳过第${i+1}行: 空行或分隔符`);
                continue;
            }
            
            // 尝试多种冒号格式的解析
            let colonIndex = -1;
            const colonVariants = [':', '：', ' : ', ' ： '];
            
            for (const colon of colonVariants) {
                colonIndex = trimmedLine.indexOf(colon);
                if (colonIndex > 0) {
                    console.log(`找到冒号分隔符 "${colon}" 在位置 ${colonIndex}`);
                    break;
                }
            }
            
            if (colonIndex > 0) {
                const key = trimmedLine.substring(0, colonIndex).trim();
                const value = trimmedLine.substring(colonIndex + (colonVariants.find(c => trimmedLine.indexOf(c) === colonIndex)?.length || 1)).trim();
                
                console.log(`提取键值对:`, {
                    key: JSON.stringify(key),
                    value: JSON.stringify(value)
                });
                
                // 扩展的字段映射表
                const fieldMapping = {
                    // 中文字段
                    '日期': 'date',
                    '时间': 'time', 
                    '姓名': 'customer_name',
                    '航班': 'flight_number',
                    '航班号': 'flight_number',
                    '客户姓名': 'customer_name',
                    '联系方式': 'customer_contact',
                    '联系电话': 'customer_contact',
                    '邮箱': 'customer_email',
                    '电子邮箱': 'customer_email',
                    '服务类型': 'service_type',
                    '乘客人数': 'passenger_count',
                    '车型': 'car_type',
                    '价格': 'ota_price',
                    '司机费用': 'driver_fee',
                    '特殊要求': 'extra_requirement',
                    '其他': 'other',
                    '备注': 'other',
                    // 英文字段
                    'pickup': 'pickup_location',
                    'drop': 'drop_location',
                    'service_type': 'service_type',
                    'passenger_count': 'passenger_count',
                    'car_type': 'car_type',
                    'ota_price': 'ota_price',
                    'customer_contact': 'customer_contact',
                    'customer_email': 'customer_email',
                    'driver_fee': 'driver_fee',
                    'extra_requirement': 'extra_requirement',
                    'other': 'other',
                    'date': 'date',
                    'time': 'time',
                    'customer_name': 'customer_name',
                    'flight_number': 'flight_number'
                };
                
                const mappedKey = fieldMapping[key] || key.toLowerCase().replace(/\s+/g, '_');
                orderData[mappedKey] = value;
                
                console.log(`字段映射成功: ${key} -> ${mappedKey} = ${value}`);
            } else {
                console.log(`第${i+1}行未找到有效的冒号分隔符:`, JSON.stringify(trimmedLine));
            }
        }
        
        console.log('=== 解析结构化文本完成 ===');
        console.log('解析后的订单数据:', orderData);
        console.log('解析出的字段数量:', Object.keys(orderData).length);
        
        return orderData;
    }
    
    /**
     * @function convertToStandardOrder - 转换为标准订单格式
     * @param {object} orderData - 原始订单数据
     * @returns {object} 标准格式的订单
     */
    convertToStandardOrder(orderData) {
        console.log('=== 开始转换为标准订单格式 ===');
        console.log('输入的orderData:', orderData);
        console.log('orderData类型:', typeof orderData);
        console.log('orderData是否为null:', orderData === null);
        console.log('orderData键列表:', orderData ? Object.keys(orderData) : 'N/A');
        
        try {
            // 验证输入数据
            if (!orderData || typeof orderData !== 'object') {
                console.log('输入数据无效，返回null');
                return null;
            }
            
            // 生成唯一的订单ID
            const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            console.log('生成订单ID:', orderId);
            
            // 处理日期时间 - 支持新旧字段名
            let orderDateTime = '';
            const serviceDate = orderData.service_date || orderData.date;
            const serviceTime = orderData.service_time || orderData.time;
            
            console.log('处理日期时间:', {
                service_date: serviceDate,
                service_time: serviceTime
            });
            
            if (serviceDate && serviceTime) {
                // 移除时间中的 "-CD" 后缀
                const cleanTime = serviceTime.replace(/-CD$/, '').trim();
                orderDateTime = `${serviceDate} ${cleanTime}`;
                console.log('组合后的日期时间:', orderDateTime);
            }
            
            // 构建标准订单对象
            console.log('开始构建标准订单对象...');
            const standardOrder = {
                id: orderId,
                customer_name: orderData.customer_name || '',
                customer_contact: orderData.customer_contact || '',
                customer_email: orderData.customer_email || '<EMAIL>',
                service_type: orderData.service_type || 'airport_pickup',
                pickup_location: orderData.pickup_location || '',
                drop_location: orderData.drop_location || '',
                service_date: serviceDate || '',
                service_time: serviceTime ? serviceTime.replace(/-CD$/, '').trim() : '',
                order_datetime: orderDateTime,
                flight_number: orderData.flight_number || '',
                passenger_count: parseInt(orderData.passenger_count) || 1,
                car_type: orderData.car_type || 'sedan',
                ota_price: parseFloat(orderData.ota_price) || 0,
                driver_fee: parseFloat(orderData.driver_fee) || 0,
                extra_requirement: orderData.extra_requirement || '',
                other: orderData.other || '',
                created_at: new Date().toISOString(),
                status: 'pending'
            };
            
            console.log('=== 标准订单对象构建完成 ===');
            console.log('标准订单对象:', standardOrder);
            console.log('标准订单对象字段数量:', Object.keys(standardOrder).length);
            console.log('关键字段检查:', {
                hasCustomerName: !!standardOrder.customer_name,
                hasPickupLocation: !!standardOrder.pickup_location,
                hasDropLocation: !!standardOrder.drop_location,
                hasServiceDate: !!standardOrder.service_date,
                hasServiceTime: !!standardOrder.service_time
            });
            
            return standardOrder;
            
        } catch (error) {
            console.error('转换订单格式时出错:', error);
            console.error('错误堆栈:', error.stack);
            console.error('输入数据:', orderData);
            return null;
        }
    }

    /**
     * 检查单个LLM服务连接
     * @param {string} llmType - LLM类型
     * @returns {Promise<boolean>} 连接状态
     */
    async checkSingleConnection(llmType) {
        const status = this.geminiStatus;
        
        // 防止重复检查
        if (status.isChecking) {
            console.log(`[LLMService] Gemini 正在检查中，跳过重复检查`);
            return status.connectionStatus === 'connected';
        }
        
        status.isChecking = true;
        
        try {
            // 检查API密钥
            const apiKey = window.config?.geminiApiKey;
                
            if (!apiKey) {
                console.warn(`[LLMService] Gemini API密钥未配置`);
                status.connectionStatus = 'disconnected';
                status.consecutiveFailures++;
                return false;
            }
            
            // 发送测试请求
            const testPrompt = "请回复'连接正常'";
            const response = await this.callGemini(testPrompt, { timeout: 10000 });
            
            if (response && response.content) {
                status.connectionStatus = 'connected';
                status.consecutiveFailures = 0;
                status.lastSuccessTime = Date.now();
                console.log(`[LLMService] Gemini 连接测试成功`);
                return true;
            } else {
                throw new Error('响应内容为空');
            }
            
        } catch (error) {
            console.error(`[LLMService] Gemini 连接测试失败:`, error);
            status.connectionStatus = 'disconnected';
            status.consecutiveFailures++;
            return false;
        } finally {
            status.lastCheckTime = Date.now();
            status.isChecking = false;
        }
    }

    /**
     * 检查LLM连接状态
     * @param {string} llmType - LLM类型 ('gemini')
     * @returns {Promise<boolean>} 连接状态
     */
    async checkConnection(llmType = 'gemini') {
        console.log(`[LLMService] 开始检查连接状态: ${llmType}`);
        
        try {
            // 检查Gemini服务
            const result = await this.checkSingleConnection('gemini');
            console.log(`[LLMService] Gemini 连接检查结果: ${result}`);
            return result;
        } catch (error) {
            console.error(`[LLMService] 连接检查失败:`, error);
            return false;
        }
    }

    /**
     * @function getStatus - 获取LLM服务状态
     * @returns {object} 服务状态
     */
    getStatus() {
        return {
            gemini: {
                ...this.geminiStatus,
                apiKeyConfigured: !!window.config?.geminiApiKey
            },
            currentLLM: this.currentLLM,
            cacheSize: this.responseCache.size
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LLMService;
} else if (typeof window !== 'undefined') {
    window.LLMService = LLMService;
}
